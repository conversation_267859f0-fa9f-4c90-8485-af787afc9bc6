"""
Setup script for Dolphin-2.7-Mixtral-8x7b with LM Studio
"""

import subprocess
import sys
import os
from lm_studio_client import LMStudioClient

def install_dependencies():
    """Install required Python packages."""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_lm_studio():
    """Check if LM Studio is running and has the Dolphin model."""
    print("\nChecking LM Studio connection...")
    
    client = LMStudioClient()
    
    # Test basic connection
    if not client.test_connection():
        print("❌ Cannot connect to LM Studio")
        print("\nPlease make sure:")
        print("1. LM Studio is running")
        print("2. Local server is started (click 'Start Server' in LM Studio)")
        print("3. Server is running on http://localhost:1234")
        return False
    
    print("✅ Connected to LM Studio!")
    
    # Check available models
    models = client.list_models()
    print(f"Available models: {models}")
    
    # Check for Dolphin model
    dolphin_variants = [
        "dolphin-2.7-mixtral-8x7b",
        "dolphin-2.7-mixtral-8x7b.Q4_K_M.gguf",
        "dolphin-2.7-mixtral-8x7b.q4_k_m.gguf"
    ]
    
    found_model = None
    for variant in dolphin_variants:
        if any(variant.lower() in model.lower() for model in models):
            found_model = next(model for model in models if variant.lower() in model.lower())
            break
    
    if found_model:
        print(f"✅ Found Dolphin model: {found_model}")
        
        # Update .env with the exact model name
        with open('.env', 'r') as f:
            content = f.read()
        
        content = content.replace('MODEL_NAME=dolphin-2.7-mixtral-8x7b', f'MODEL_NAME={found_model}')
        
        with open('.env', 'w') as f:
            f.write(content)
        
        print(f"✅ Updated .env with model name: {found_model}")
        return True
    else:
        print("❌ Dolphin-2.7-Mixtral model not found")
        print("\nPlease:")
        print("1. Download dolphin-2.7-mixtral-8x7b model in LM Studio")
        print("2. Load the model in LM Studio")
        print("3. Make sure the model is active")
        return False

def test_model():
    """Test the Dolphin model with a simple query."""
    print("\nTesting Dolphin model...")
    
    try:
        client = LMStudioClient()
        response = client.simple_chat(
            "Hello! Please introduce yourself briefly.",
            "You are Dolphin, an AI assistant based on Mixtral-8x7b."
        )
        
        print("✅ Model test successful!")
        print(f"Dolphin says: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🐬 Dolphin-2.7-Mixtral-8x7b Setup")
    print("=" * 40)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        return
    
    # Step 2: Check LM Studio
    if not check_lm_studio():
        return
    
    # Step 3: Test the model
    if not test_model():
        return
    
    print("\n🎉 Setup complete!")
    print("\nYou can now run:")
    print("- python lm_studio_client.py (basic test)")
    print("- python dolphin_examples.py (interactive examples)")
    
    # Ask if user wants to run examples
    run_examples = input("\nWould you like to run the examples now? (y/n): ").lower().strip()
    if run_examples in ['y', 'yes']:
        print("\nStarting Dolphin examples...")
        os.system("python dolphin_examples.py")

if __name__ == "__main__":
    main()
