"""
Dolphin-2.7-Mixtral-8x7b Examples
Showcasing the capabilities of the Dolphin model through various use cases.
"""

from lm_studio_client import LMStudioClient
import json
import time

class DolphinAssistant:
    """Specialized assistant using Dolphin-2.7-Mixtral-8x7b model."""
    
    def __init__(self):
        self.client = LMStudioClient()
        self.model_name = "dolphin-2.7-mixtral-8x7b"
        
    def test_connection(self):
        """Test if the Dolphin model is available."""
        if not self.client.test_connection():
            print("❌ Cannot connect to LM Studio")
            return False
            
        models = self.client.list_models()
        if self.model_name not in models:
            print(f"❌ Dolphin model not found. Available models: {models}")
            print("Make sure you have loaded dolphin-2.7-mixtral-8x7b in LM Studio")
            return False
            
        print("✅ Dolphin-2.7-Mixtral-8x7b is ready!")
        return True
    
    def code_analysis(self, code: str, language: str = "python"):
        """Analyze code and provide insights."""
        system_prompt = """You are an expert code reviewer. Analyze the provided code and give:
1. Code quality assessment
2. Potential improvements
3. Security considerations
4. Performance suggestions
Be concise but thorough."""
        
        user_prompt = f"Please analyze this {language} code:\n\n```{language}\n{code}\n```"
        
        response = self.client.simple_chat(user_prompt, system_prompt)
        return response
    
    def creative_writing(self, prompt: str, style: str = "creative"):
        """Generate creative content."""
        system_prompt = f"""You are a skilled creative writer. Write in a {style} style.
Be imaginative, engaging, and well-structured."""
        
        response = self.client.simple_chat(prompt, system_prompt)
        return response
    
    def problem_solving(self, problem: str):
        """Solve complex problems step by step."""
        system_prompt = """You are an expert problem solver. Break down complex problems into steps:
1. Understand the problem
2. Identify key components
3. Develop a solution strategy
4. Provide implementation details
5. Consider edge cases"""
        
        response = self.client.simple_chat(problem, system_prompt)
        return response
    
    def data_analysis_helper(self, data_description: str, question: str):
        """Help with data analysis questions."""
        system_prompt = """You are a data scientist. Help analyze data and answer questions.
Provide practical insights and suggest appropriate analysis methods."""
        
        prompt = f"Data: {data_description}\n\nQuestion: {question}"
        response = self.client.simple_chat(prompt, system_prompt)
        return response
    
    def chat_conversation(self):
        """Interactive chat session with Dolphin."""
        print("\n🐬 Starting chat with Dolphin-2.7-Mixtral-8x7b")
        print("Type 'quit' to exit, 'clear' to start fresh conversation\n")
        
        conversation = []
        
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'clear':
                conversation = []
                print("Conversation cleared!\n")
                continue
            elif not user_input:
                continue
            
            # Add user message to conversation
            conversation.append({"role": "user", "content": user_input})
            
            try:
                # Get response from Dolphin
                print("Dolphin: ", end="", flush=True)
                response = self.client.chat_completion(
                    messages=[{"role": "system", "content": "You are Dolphin, a helpful and intelligent AI assistant."}] + conversation,
                    temperature=0.7,
                    max_tokens=1000
                )
                
                assistant_response = response.choices[0].message.content
                print(assistant_response)
                
                # Add assistant response to conversation
                conversation.append({"role": "assistant", "content": assistant_response})
                
            except Exception as e:
                print(f"Error: {e}")
            
            print()  # Add spacing

def run_examples():
    """Run various examples with Dolphin."""
    assistant = DolphinAssistant()
    
    if not assistant.test_connection():
        return
    
    print("\n🐬 Dolphin-2.7-Mixtral-8x7b Examples\n")
    
    # Example 1: Code Analysis
    print("=" * 50)
    print("Example 1: Code Analysis")
    print("=" * 50)
    
    sample_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

result = fibonacci(10)
print(result)
"""
    
    analysis = assistant.code_analysis(sample_code)
    print(analysis)
    
    # Example 2: Creative Writing
    print("\n" + "=" * 50)
    print("Example 2: Creative Writing")
    print("=" * 50)
    
    story = assistant.creative_writing(
        "Write a short story about an AI that discovers it can dream",
        "science fiction"
    )
    print(story)
    
    # Example 3: Problem Solving
    print("\n" + "=" * 50)
    print("Example 3: Problem Solving")
    print("=" * 50)
    
    problem = """I need to optimize a web application that's running slowly. 
    The app has a database with 1 million records, and users are complaining about slow search results."""
    
    solution = assistant.problem_solving(problem)
    print(solution)

def main():
    """Main function with menu options."""
    assistant = DolphinAssistant()
    
    if not assistant.test_connection():
        return
    
    while True:
        print("\n🐬 Dolphin-2.7-Mixtral-8x7b Assistant")
        print("1. Run Examples")
        print("2. Interactive Chat")
        print("3. Code Analysis")
        print("4. Creative Writing")
        print("5. Problem Solving")
        print("6. Exit")
        
        choice = input("\nSelect an option (1-6): ").strip()
        
        if choice == "1":
            run_examples()
        elif choice == "2":
            assistant.chat_conversation()
        elif choice == "3":
            code = input("Enter your code: ")
            language = input("Programming language (default: python): ") or "python"
            result = assistant.code_analysis(code, language)
            print(f"\nAnalysis:\n{result}")
        elif choice == "4":
            prompt = input("Enter your creative writing prompt: ")
            style = input("Writing style (default: creative): ") or "creative"
            result = assistant.creative_writing(prompt, style)
            print(f"\nStory:\n{result}")
        elif choice == "5":
            problem = input("Describe your problem: ")
            result = assistant.problem_solving(problem)
            print(f"\nSolution:\n{result}")
        elif choice == "6":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
