# Local LLM with LM Studio

This project provides a simple interface to interact with local LLMs running through LM Studio.

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure LM Studio

1. Open LM Studio
2. Load your preferred model
3. Start the local server (usually runs on `http://localhost:1234`)
4. Note the model name from LM Studio

### 3. Configure Environment

Edit the `.env` file with your settings:

```env
LM_STUDIO_BASE_URL=http://localhost:1234/v1
LM_STUDIO_API_KEY=lm-studio
MODEL_NAME=your-actual-model-name
```

## Usage

### Basic Example

```python
from lm_studio_client import LMStudioClient

# Initialize client
client = LMStudioClient()

# Simple chat
response = client.simple_chat("What is the capital of France?")
print(response)
```

### Advanced Usage

```python
# Chat with conversation history
messages = [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello!"},
    {"role": "assistant", "content": "Hi! How can I help you today?"},
    {"role": "user", "content": "What's the weather like?"}
]

response = client.chat_completion(messages, temperature=0.7)
print(response.choices[0].message.content)
```

## Testing

Run the example script to test your setup:

```bash
python lm_studio_client.py
```

This will:
- Test connection to LM Studio
- List available models
- Send a simple chat message

## Troubleshooting

1. **Connection Error**: Make sure LM Studio is running and the server is started
2. **Model Not Found**: Check the model name in LM Studio and update your `.env` file
3. **Port Issues**: LM Studio typically uses port 1234, but check your LM Studio settings

## Next Steps

You can extend this client for various use cases:
- Document analysis
- Code generation
- Creative writing
- Question answering systems
- Custom chatbots
