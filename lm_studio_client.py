"""
LM Studio Local LLM Client
A simple client to interact with LM Studio's local API server.
"""

import os
import requests
from openai import OpenAI
from dotenv import load_dotenv
from typing import Optional, Dict, Any, List

# Load environment variables
load_dotenv()

class LMStudioClient:
    """Client for interacting with LM Studio's local API server."""
    
    def __init__(self, 
                 base_url: Optional[str] = None,
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = None):
        """
        Initialize the LM Studio client.
        
        Args:
            base_url: LM Studio server URL (default: http://localhost:1234/v1)
            api_key: API key (default: lm-studio)
            model_name: Name of the model to use
        """
        self.base_url = base_url or os.getenv("LM_STUDIO_BASE_URL", "http://localhost:1234/v1")
        self.api_key = api_key or os.getenv("LM_STUDIO_API_KEY", "lm-studio")
        self.model_name = model_name or os.getenv("MODEL_NAME")
        
        # Initialize OpenAI client pointing to LM Studio
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key
        )
    
    def test_connection(self) -> bool:
        """Test if LM Studio server is running and accessible."""
        try:
            response = requests.get(f"{self.base_url.rstrip('/v1')}/health", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            try:
                # Try to list models as alternative health check
                models = self.list_models()
                return len(models) > 0
            except:
                return False
    
    def list_models(self) -> List[str]:
        """List available models in LM Studio."""
        try:
            models = self.client.models.list()
            return [model.id for model in models.data]
        except Exception as e:
            print(f"Error listing models: {e}")
            return []
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       model: Optional[str] = None,
                       temperature: float = 0.7,
                       max_tokens: Optional[int] = None,
                       stream: bool = False) -> Any:
        """
        Send a chat completion request to LM Studio.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model name to use (uses default if not specified)
            temperature: Sampling temperature (0.0 to 1.0)
            max_tokens: Maximum tokens to generate
            stream: Whether to stream the response
            
        Returns:
            Chat completion response
        """
        model_to_use = model or self.model_name
        if not model_to_use:
            available_models = self.list_models()
            if available_models:
                model_to_use = available_models[0]
                print(f"No model specified, using: {model_to_use}")
            else:
                raise ValueError("No model specified and no models available")
        
        try:
            response = self.client.chat.completions.create(
                model=model_to_use,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            return response
        except Exception as e:
            print(f"Error in chat completion: {e}")
            raise
    
    def simple_chat(self, prompt: str, system_message: Optional[str] = None) -> str:
        """
        Simple chat interface for quick interactions.
        
        Args:
            prompt: User prompt
            system_message: Optional system message
            
        Returns:
            Model response as string
        """
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})
        
        response = self.chat_completion(messages)
        return response.choices[0].message.content

def main():
    """Example usage of the LM Studio client."""
    client = LMStudioClient()
    
    # Test connection
    print("Testing connection to LM Studio...")
    if not client.test_connection():
        print("❌ Cannot connect to LM Studio. Make sure it's running on http://localhost:1234")
        return
    
    print("✅ Connected to LM Studio!")
    
    # List available models
    models = client.list_models()
    print(f"Available models: {models}")
    
    # Simple chat example
    try:
        response = client.simple_chat(
            prompt="Hello! Can you tell me a short joke?",
            system_message="You are a helpful assistant."
        )
        print(f"\nModel response: {response}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
